# Project Guidelines

## Project Overview

This template serves as a starter project for websites built using the Brizy platform. The workflow is as follows:

1. Website code is initially created and exported from the Brizy platform
2. The exported code is imported into this template (replacing previous versions)
3. We then enhance the website with additional functionality and improvements that are beyond the capabilities of the Brizy platform due to its limitations
4. This approach allows us to leverage Brizy's visual builder while extending its functionality through custom code

### Version Control Workflow

This project uses a straightforward Git workflow to accommodate Brizy's export process:

1. Each time we receive a new export from Brizy, we completely replace the affected files
2. We then re-apply our custom modifications according to the `MODIFICATIONS.md` file
3. All custom modifications are technical fixes that remain consistent across Brizy exports
4. Version history is maintained through Git commits on the main branch

## HTML Formatting Guidelines

### Important: Do Not Modify HTML Formatting

-   **NEVER** reformat or prettify HTML files from Brizy exports
-   Brizy generates HTML with specific class structures and inline styles that are tightly coupled with its rendering engine
-   Even minor formatting changes can break the layout and functionality of Brizy-generated components
-   All HTML files are excluded in `.prettierignore` to prevent accidental formatting
-   When editing HTML, maintain the exact spacing, indentation, and line breaks of the original file
-   If you need to add custom elements, follow the existing formatting pattern precisely

### Working with Brizy HTML

-   Add custom code in dedicated sections when possible, rather than modifying existing Brizy elements
-   When modifying Brizy elements is necessary, make minimal changes and preserve all class names and attributes
-   Test thoroughly after any HTML modifications to ensure the layout renders correctly

## CSS and JavaScript Guidelines

### Prettier Configuration

-   Do NOT format HTML files with Prettier as it breaks the CSS in Brizy platform projects
-   All HTML files are excluded in `.prettierignore` for this reason
-   CSS classes and inline styles in Brizy HTML are tightly coupled with the platform's rendering engine

## Standard Modifications

All standard modifications that need to be applied after each Brizy export are documented in the `MODIFICATIONS.md` file. These modifications address technical limitations of the Brizy platform and remain consistent across different exports.

### When to Apply Modifications

Apply the modifications documented in `MODIFICATIONS.md` in the following scenarios:

-   After importing a new Brizy export
-   When creating a new project based on this template
-   If you notice any Brizy-related issues that match the documented fixes

### Git Workflow for Brizy Updates

1. Backup your current project state (optional)
2. Import the new Brizy export files, replacing the existing files
3. Re-apply all modifications from the `MODIFICATIONS.md` file
4. Test thoroughly to ensure all modifications work as expected
5. Commit changes with a descriptive message (e.g., `"Update Brizy export and re-apply standard modifications"`)
